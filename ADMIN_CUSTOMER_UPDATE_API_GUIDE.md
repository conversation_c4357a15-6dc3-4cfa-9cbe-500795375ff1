# Admin Customer Update API Guide

## API Endpoint
```
PUT /v1/admin/customer
```

## Authentication
- **Required**: Bearer token with `adminRootAccess` permission
- **Header**: `Authorization: Bearer <admin_token>`

## Content Types Supported
1. **JSON**: `application/json` (for data without file upload)
2. **Multipart**: `multipart/form-data` (for data with file upload)

## Request Data Structure

### Required Fields
```json
{
  "customer": "ObjectId",        // Customer ID to update (24-character MongoDB ObjectId)
  "email": "string",             // Valid email address (must be unique)
  "username": "string",          // Username (must be unique)
  "emailPermission": boolean     // Email permission flag
}
```

### Optional Fields
```json
{
  "photo": "string|file",        // Photo URL or file upload
  "bio": "string",               // Bio text (max 500 characters)
  "notificationPermission": boolean,  // Notification permission flag
  "latitude": number,            // Latitude (-90 to 90)
  "longitude": number,           // Longitude (-180 to 180)
  "address": {                   // Address object
    "pincode": "string",
    "locality": "string", 
    "address_line": "string",
    "city": "string",
    "state": "string",
    "landmark": "string"
  }
}
```

## Photo Upload Requirements
- **Field name**: `photo`
- **Allowed types**: `image/jpeg`, `image/jpg`, `image/png`, `image/webp`, `image/svg+xml`
- **Size limits**: 20KB - 2MB
- **Upload directory**: `uploads/customers/`
- **Multiple files**: No (single file only)

## Example Requests

### 1. JSON Request (without photo)
```bash
curl -X PUT "http://localhost:3000/v1/admin/customer" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "customer": "60f7b3b3b3b3b3b3b3b3b3b3",
    "email": "<EMAIL>",
    "username": "updated_username",
    "emailPermission": true,
    "bio": "Updated customer bio",
    "notificationPermission": true,
    "latitude": 40.7128,
    "longitude": -74.0060,
    "address": {
      "pincode": "10001",
      "locality": "Manhattan",
      "address_line": "123 Main Street",
      "city": "New York",
      "state": "NY",
      "landmark": "Near Central Park"
    }
  }'
```

### 2. Multipart Request (with photo)
```bash
curl -X PUT "http://localhost:3000/v1/admin/customer" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -F "customer=60f7b3b3b3b3b3b3b3b3b3b3" \
  -F "email=<EMAIL>" \
  -F "username=updated_username" \
  -F "emailPermission=true" \
  -F "bio=Updated customer bio" \
  -F "notificationPermission=true" \
  -F "latitude=40.7128" \
  -F "longitude=-74.0060" \
  -F "address[pincode]=10001" \
  -F "address[locality]=Manhattan" \
  -F "address[address_line]=123 Main Street" \
  -F "address[city]=New York" \
  -F "address[state]=NY" \
  -F "address[landmark]=Near Central Park" \
  -F "photo=@/path/to/image.jpg"
```

## Response Format

### Success Response (200)
```json
{
  "status": true,
  "code": 200,
  "message": "Customer updated successfully!",
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "user": {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b4",
      "email": "<EMAIL>",
      "role": "customer"
    },
    "username": "updated_username",
    "emailPermission": true,
    "photo": "uploads/customers/photo-1234567890.jpg",
    "bio": "Updated customer bio",
    "notificationPermission": true,
    "location": {
      "type": "Point",
      "coordinates": [-74.0060, 40.7128]
    },
    "address": {
      "pincode": "10001",
      "locality": "Manhattan",
      "address_line": "123 Main Street",
      "city": "New York",
      "state": "NY",
      "landmark": "Near Central Park"
    },
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T12:00:00.000Z"
  }
}
```

### Error Responses

#### 400 - Validation Error
```json
{
  "status": false,
  "code": 400,
  "message": "Validation error details",
  "data": []
}
```

#### 401 - Unauthorized
```json
{
  "status": false,
  "code": 401,
  "message": "Please authenticate",
  "data": []
}
```

#### 403 - Forbidden
```json
{
  "status": false,
  "code": 403,
  "message": "Forbidden",
  "data": []
}
```

## Testing Steps

### Prerequisites
1. **Start the server**: `npm run dev`
2. **Seed database**: `npm run seed` (creates admin user: <EMAIL> / password123)
3. **Get admin token**: Login as admin to get Bearer token

### Step 1: Get Admin Token
```bash
curl -X POST "http://localhost:3000/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Step 2: Get Customer List
```bash
curl -X GET "http://localhost:3000/v1/admin/customer" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### Step 3: Update Customer
Use the customer ID from step 2 and update using the examples above.

## Validation Rules

### Email
- Must be valid email format
- Must be unique across all users
- Required field

### Username  
- Must be unique across all customers
- Required field

### Photo
- Optional field
- Can be URL string or file upload
- File size: 20KB - 2MB
- Allowed formats: jpeg, jpg, png, webp, svg+xml

### Bio
- Optional field
- Maximum 500 characters

### Location
- Latitude: -90 to 90
- Longitude: -180 to 180
- Automatically converted to GeoJSON Point format

### Address
- All fields are optional strings
- Nested object structure

## Common Issues

1. **"Please authenticate"**: Missing or invalid Bearer token
2. **"Forbidden"**: Token doesn't have adminRootAccess permission  
3. **"Validation error"**: Check required fields and data formats
4. **"Email already exists"**: Email must be unique
5. **"Username already exists"**: Username must be unique
6. **File upload errors**: Check file size and format requirements

## Code Implementation

The API uses:
- **Route**: `src/routes/v1/admin.route.js` (line 38-42)
- **Controller**: `src/controllers/customer.controller.js` (update method)
- **Validation**: `src/validations/customer.validation.js` (update schema)
- **Model**: `src/models/customer.model.js`
- **Middleware**: File upload, authentication, validation
