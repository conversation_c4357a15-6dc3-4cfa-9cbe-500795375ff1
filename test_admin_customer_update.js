const request = require('supertest');
const app = require('./src/app');
const { Customer, User } = require('./src/models');

// Test data for customer update
const testCustomerUpdate = async () => {
  try {
    console.log('🚀 Testing Admin Customer Update API...\n');

    // First, let's create a test customer to update
    console.log('1. Creating test customer...');
    
    // Create user first
    const testUser = {
      email: '<EMAIL>',
      password: 'password123',
      role: 'customer'
    };

    const userResponse = await request(app)
      .post('/v1/auth/register')
      .send(testUser);

    if (userResponse.status !== 201) {
      console.log('❌ Failed to create test user:', userResponse.body);
      return;
    }

    console.log('✅ Test user created successfully');

    // Login to get admin token (you'll need to replace with actual admin credentials)
    console.log('2. Getting admin authentication token...');
    
    const adminLogin = await request(app)
      .post('/v1/auth/login')
      .send({
        email: '<EMAIL>', // Replace with actual admin email
        password: 'adminpassword123' // Replace with actual admin password
      });

    if (adminLogin.status !== 200) {
      console.log('❌ Admin login failed:', adminLogin.body);
      console.log('Please update admin credentials in the test file');
      return;
    }

    const adminToken = adminLogin.body.tokens.access.token;
    console.log('✅ Admin token obtained');

    // Get customer ID from database
    const customer = await Customer.findOne({ user: userResponse.body.user.id });
    if (!customer) {
      console.log('❌ Customer not found in database');
      return;
    }

    console.log('3. Testing customer update API...');

    // Test data for update
    const updateData = {
      customer: customer._id.toString(),
      email: '<EMAIL>',
      username: 'updated_username_' + Date.now(),
      emailPermission: true,
      bio: 'Updated bio for testing',
      notificationPermission: true,
      latitude: 40.7128,
      longitude: -74.0060,
      address: {
        pincode: '10001',
        locality: 'Manhattan',
        address_line: '123 Test Street',
        city: 'New York',
        state: 'NY',
        landmark: 'Near Central Park'
      }
    };

    console.log('📤 Sending update request with data:', JSON.stringify(updateData, null, 2));

    const updateResponse = await request(app)
      .put('/v1/admin/customer')
      .set('Authorization', `Bearer ${adminToken}`)
      .send(updateData);

    console.log('\n📥 Response Status:', updateResponse.status);
    console.log('📥 Response Body:', JSON.stringify(updateResponse.body, null, 2));

    if (updateResponse.status === 200) {
      console.log('✅ Customer update API is working successfully!');
      
      // Verify the update in database
      const updatedCustomer = await Customer.findById(customer._id).populate('user');
      console.log('\n🔍 Updated customer in database:');
      console.log('- Email:', updatedCustomer.user.email);
      console.log('- Username:', updatedCustomer.username);
      console.log('- Bio:', updatedCustomer.bio);
      console.log('- Email Permission:', updatedCustomer.emailPermission);
      console.log('- Notification Permission:', updatedCustomer.notificationPermission);
      console.log('- Location:', updatedCustomer.location);
      console.log('- Address:', updatedCustomer.address);
      
    } else {
      console.log('❌ Customer update failed');
      
      if (updateResponse.status === 400) {
        console.log('🔍 Validation errors:', updateResponse.body.message);
      } else if (updateResponse.status === 401) {
        console.log('🔍 Authentication failed - check admin token');
      } else if (updateResponse.status === 403) {
        console.log('🔍 Authorization failed - check admin permissions');
      }
    }

    // Cleanup
    console.log('\n4. Cleaning up test data...');
    await Customer.findByIdAndDelete(customer._id);
    await User.findByIdAndDelete(userResponse.body.user.id);
    console.log('✅ Test data cleaned up');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
};

// Test with file upload
const testCustomerUpdateWithPhoto = async () => {
  console.log('\n🖼️ Testing Customer Update with Photo Upload...\n');
  
  try {
    // This would require actual file upload testing
    // For now, just showing the structure
    console.log('📝 Required data for photo upload:');
    console.log('- Use multipart/form-data');
    console.log('- Include photo field with image file');
    console.log('- Supported formats: jpeg, jpg, png, webp, svg+xml');
    console.log('- Size: 20KB - 2MB');
    console.log('- Field name: "photo"');
    
  } catch (error) {
    console.error('❌ Photo upload test failed:', error.message);
  }
};

// Run tests
const runTests = async () => {
  console.log('🧪 ADMIN CUSTOMER UPDATE API TESTS');
  console.log('=====================================\n');
  
  await testCustomerUpdate();
  await testCustomerUpdateWithPhoto();
  
  console.log('\n📋 SUMMARY:');
  console.log('=====================================');
  console.log('✅ API Endpoint: PUT /v1/admin/customer');
  console.log('🔐 Authentication: Bearer token (adminRootAccess required)');
  console.log('📤 Content-Type: multipart/form-data (for file upload) or application/json');
  console.log('\n📝 Required Fields:');
  console.log('- customer: ObjectId (customer ID to update)');
  console.log('- email: string (valid email, unique)');
  console.log('- username: string (unique)');
  console.log('- emailPermission: boolean');
  console.log('\n📝 Optional Fields:');
  console.log('- photo: file or URL string');
  console.log('- bio: string (max 500 chars)');
  console.log('- notificationPermission: boolean');
  console.log('- latitude: number');
  console.log('- longitude: number');
  console.log('- address: object with pincode, locality, address_line, city, state, landmark');
  
  process.exit(0);
};

// Run if called directly
if (require.main === module) {
  runTests();
}

module.exports = { testCustomerUpdate, testCustomerUpdateWithPhoto };
