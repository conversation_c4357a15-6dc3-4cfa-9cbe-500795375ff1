const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/v1';
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'password123';

// Test the admin customer update API
async function testAdminCustomerUpdateAPI() {
  try {
    console.log('🚀 Testing Admin Customer Update API...\n');

    // Step 1: Login as admin
    console.log('1. Logging in as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD
    });

    if (loginResponse.status !== 200) {
      console.log('❌ Admin login failed');
      return;
    }

    const adminToken = loginResponse.data.tokens.access.token;
    console.log('✅ Admin login successful');

    // Step 2: Get list of customers
    console.log('2. Getting customer list...');
    const customersResponse = await axios.get(`${BASE_URL}/admin/customer`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });

    if (customersResponse.status !== 200 || !customersResponse.data.data || customersResponse.data.data.length === 0) {
      console.log('❌ No customers found. Please seed the database first.');
      console.log('Run: npm run seed');
      return;
    }

    const firstCustomer = customersResponse.data.data[0];
    console.log('✅ Found customers. Using first customer for testing:');
    console.log(`   Customer ID: ${firstCustomer._id}`);
    console.log(`   Username: ${firstCustomer.username}`);
    console.log(`   Email: ${firstCustomer.user?.email || 'N/A'}`);

    // Step 3: Test customer update
    console.log('\n3. Testing customer update...');
    
    const updateData = {
      customer: firstCustomer._id,
      email: `updated.${Date.now()}@example.com`,
      username: `updated_user_${Date.now()}`,
      emailPermission: true,
      bio: 'Updated bio for API testing',
      notificationPermission: true,
      latitude: 40.7128,
      longitude: -74.0060,
      address: {
        pincode: '10001',
        locality: 'Manhattan',
        address_line: '123 Updated Street',
        city: 'New York',
        state: 'NY',
        landmark: 'Near Times Square'
      }
    };

    console.log('📤 Sending update request...');
    console.log('Data:', JSON.stringify(updateData, null, 2));

    const updateResponse = await axios.put(`${BASE_URL}/admin/customer`, updateData, {
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('\n📥 Response Status:', updateResponse.status);
    console.log('📥 Response Data:', JSON.stringify(updateResponse.data, null, 2));

    if (updateResponse.status === 200) {
      console.log('\n✅ Customer update API is working successfully!');
      
      // Verify the update
      const verifyResponse = await axios.get(`${BASE_URL}/admin/customer`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
      
      const updatedCustomer = verifyResponse.data.data.find(c => c._id === firstCustomer._id);
      if (updatedCustomer) {
        console.log('\n🔍 Verified updated customer data:');
        console.log('- Username:', updatedCustomer.username);
        console.log('- Email Permission:', updatedCustomer.emailPermission);
        console.log('- Bio:', updatedCustomer.bio);
        console.log('- Notification Permission:', updatedCustomer.notificationPermission);
        console.log('- Location:', updatedCustomer.location);
        console.log('- Address:', updatedCustomer.address);
      }
    } else {
      console.log('❌ Customer update failed');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 400) {
        console.log('\n🔍 This is likely a validation error. Check the required fields.');
      } else if (error.response.status === 401) {
        console.log('\n🔍 Authentication failed. Check admin credentials.');
      } else if (error.response.status === 403) {
        console.log('\n🔍 Authorization failed. Check admin permissions.');
      } else if (error.response.status === 500) {
        console.log('\n🔍 Server error. Check server logs.');
      }
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n🔍 Connection refused. Make sure the server is running on port 3000.');
      console.log('Run: npm start or npm run dev');
    }
  }
}

// Test with file upload (multipart/form-data)
async function testCustomerUpdateWithPhoto() {
  console.log('\n\n🖼️ Testing Customer Update with Photo Upload...\n');
  
  try {
    console.log('📝 For photo upload testing, you need to:');
    console.log('1. Use multipart/form-data content type');
    console.log('2. Include a photo file in the request');
    console.log('3. Use tools like Postman, curl, or form-data library');
    console.log('\nExample curl command:');
    console.log(`curl -X PUT "${BASE_URL}/admin/customer" \\`);
    console.log(`  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \\`);
    console.log(`  -F "customer=CUSTOMER_ID" \\`);
    console.log(`  -F "email=<EMAIL>" \\`);
    console.log(`  -F "username=testuser" \\`);
    console.log(`  -F "emailPermission=true" \\`);
    console.log(`  -F "photo=@/path/to/image.jpg"`);
    
  } catch (error) {
    console.error('❌ Photo upload test failed:', error.message);
  }
}

// Main function
async function runTests() {
  console.log('🧪 ADMIN CUSTOMER UPDATE API TESTS');
  console.log('=====================================\n');
  
  await testAdminCustomerUpdateAPI();
  await testCustomerUpdateWithPhoto();
  
  console.log('\n\n📋 API SUMMARY:');
  console.log('=====================================');
  console.log('✅ Endpoint: PUT /v1/admin/customer');
  console.log('🔐 Auth: Bearer token (adminRootAccess required)');
  console.log('📤 Content-Type: application/json or multipart/form-data');
  console.log('\n📝 Required Fields:');
  console.log('- customer: ObjectId (customer ID to update)');
  console.log('- email: string (valid email, must be unique)');
  console.log('- username: string (must be unique)');
  console.log('- emailPermission: boolean');
  console.log('\n📝 Optional Fields:');
  console.log('- photo: file upload or URL string');
  console.log('- bio: string (max 500 characters)');
  console.log('- notificationPermission: boolean');
  console.log('- latitude: number (-90 to 90)');
  console.log('- longitude: number (-180 to 180)');
  console.log('- address: object with pincode, locality, address_line, city, state, landmark');
  
  console.log('\n🔧 Photo Upload Requirements:');
  console.log('- Field name: "photo"');
  console.log('- Allowed types: jpeg, jpg, png, webp, svg+xml');
  console.log('- Size: 20KB - 2MB');
  console.log('- Upload path: uploads/customers/');
}

// Run tests if called directly
if (require.main === module) {
  runTests().then(() => {
    console.log('\n✅ Tests completed!');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Tests failed:', error.message);
    process.exit(1);
  });
}

module.exports = { testAdminCustomerUpdateAPI, testCustomerUpdateWithPhoto };
