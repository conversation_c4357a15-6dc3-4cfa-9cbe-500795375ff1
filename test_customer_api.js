const http = require('http');
const https = require('https');

// Configuration
const BASE_URL = 'http://localhost:3000';
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'password123';

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const protocol = options.protocol === 'https:' ? https : http;
    
    const req = protocol.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonBody = body ? JSON.parse(body) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonBody
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: body
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test admin login
async function testAdminLogin() {
  console.log('🔐 Testing admin login...');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/v1/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  const loginData = {
    email: ADMIN_EMAIL,
    password: ADMIN_PASSWORD
  };
  
  try {
    const response = await makeRequest(options, loginData);
    
    if (response.status === 200 && response.data.tokens) {
      console.log('✅ Admin login successful');
      return response.data.tokens.access.token;
    } else {
      console.log('❌ Admin login failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Login request failed:', error.message);
    return null;
  }
}

// Get customer list
async function getCustomers(token) {
  console.log('📋 Getting customer list...');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/v1/admin/customer',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  };
  
  try {
    const response = await makeRequest(options);
    
    if (response.status === 200 && response.data.data) {
      console.log(`✅ Found ${response.data.data.length} customers`);
      return response.data.data;
    } else {
      console.log('❌ Failed to get customers:', response.data);
      return [];
    }
  } catch (error) {
    console.log('❌ Get customers request failed:', error.message);
    return [];
  }
}

// Test customer update
async function testCustomerUpdate(token, customerId) {
  console.log('🔄 Testing customer update...');
  
  const updateData = {
    customer: customerId,
    email: `updated.${Date.now()}@example.com`,
    username: `updated_user_${Date.now()}`,
    emailPermission: true,
    bio: 'Updated bio for API testing',
    notificationPermission: true,
    latitude: 40.7128,
    longitude: -74.0060,
    address: {
      pincode: '10001',
      locality: 'Manhattan',
      address_line: '123 Updated Street',
      city: 'New York',
      state: 'NY',
      landmark: 'Near Times Square'
    }
  };
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/v1/admin/customer',
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };
  
  console.log('📤 Sending update data:', JSON.stringify(updateData, null, 2));
  
  try {
    const response = await makeRequest(options, updateData);
    
    console.log(`📥 Response Status: ${response.status}`);
    console.log('📥 Response Data:', JSON.stringify(response.data, null, 2));
    
    if (response.status === 200) {
      console.log('✅ Customer update successful!');
      return true;
    } else {
      console.log('❌ Customer update failed');
      
      // Provide specific error guidance
      if (response.status === 400) {
        console.log('🔍 Validation error - check required fields and data formats');
      } else if (response.status === 401) {
        console.log('🔍 Authentication failed - check admin token');
      } else if (response.status === 403) {
        console.log('🔍 Authorization failed - check admin permissions');
      } else if (response.status === 500) {
        console.log('🔍 Server error - check server logs');
      }
      
      return false;
    }
  } catch (error) {
    console.log('❌ Update request failed:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🧪 ADMIN CUSTOMER UPDATE API TEST');
  console.log('==================================\n');
  
  try {
    // Test server connectivity
    console.log('🌐 Testing server connectivity...');
    const healthOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/v1/docs',
      method: 'GET'
    };
    
    try {
      await makeRequest(healthOptions);
      console.log('✅ Server is running on port 3000\n');
    } catch (error) {
      console.log('❌ Server is not running. Please start with: npm run dev\n');
      return;
    }
    
    // Step 1: Admin login
    const adminToken = await testAdminLogin();
    if (!adminToken) {
      console.log('\n❌ Cannot proceed without admin token');
      console.log('💡 Make sure to seed the database first: npm run seed');
      return;
    }
    
    console.log(''); // Empty line for readability
    
    // Step 2: Get customers
    const customers = await getCustomers(adminToken);
    if (customers.length === 0) {
      console.log('\n❌ No customers found for testing');
      console.log('💡 Please seed the database first: npm run seed');
      return;
    }
    
    const firstCustomer = customers[0];
    console.log(`🎯 Using customer for testing:`);
    console.log(`   ID: ${firstCustomer._id}`);
    console.log(`   Username: ${firstCustomer.username}`);
    console.log(`   Email: ${firstCustomer.user?.email || 'N/A'}\n`);
    
    // Step 3: Test update
    const updateSuccess = await testCustomerUpdate(adminToken, firstCustomer._id);
    
    console.log('\n📋 TEST SUMMARY:');
    console.log('================');
    console.log(`✅ Server connectivity: OK`);
    console.log(`✅ Admin authentication: OK`);
    console.log(`✅ Customer data retrieval: OK`);
    console.log(`${updateSuccess ? '✅' : '❌'} Customer update: ${updateSuccess ? 'OK' : 'FAILED'}`);
    
    if (updateSuccess) {
      console.log('\n🎉 All tests passed! The API is working correctly.');
    } else {
      console.log('\n⚠️  Customer update failed. Check the error details above.');
    }
    
  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  }
  
  console.log('\n📖 For detailed API documentation, see: ADMIN_CUSTOMER_UPDATE_API_GUIDE.md');
}

// Run tests if called directly
if (require.main === module) {
  runTests().then(() => {
    console.log('\n✅ Test completed!');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  });
}

module.exports = { runTests, testAdminLogin, getCustomers, testCustomerUpdate };
